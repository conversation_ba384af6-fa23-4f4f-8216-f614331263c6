// 测试RTC修正逻辑的简单验证
#include <stdio.h>
#include <stdint.h>

#define RTC_CLOCK_CORRECTION_FACTOR 1.17f
#define RTC_MAX_SLEEP_SECONDS_RAW 65535
#define RTC_MAX_LOGICAL_SLEEP_SECONDS (uint32_t)(RTC_MAX_SLEEP_SECONDS_RAW / RTC_CLOCK_CORRECTION_FACTOR)

uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds)
{
    uint32_t max_logical_time = RTC_MAX_LOGICAL_SLEEP_SECONDS;
    uint32_t actual_logical_time;
    uint32_t physical_rtc_time;
    
    // 确定实际能执行的逻辑时间
    if (logical_seconds <= max_logical_time) {
        actual_logical_time = logical_seconds;
    } else {
        actual_logical_time = max_logical_time;
        printf("RTC correction limited logical time from %lu to %lu seconds\r\n", 
               logical_seconds, actual_logical_time);
    }
    
    // 计算RTC应该设置的物理时间
    physical_rtc_time = (uint32_t)(actual_logical_time * RTC_CLOCK_CORRECTION_FACTOR);
    
    // 安全检查，防止溢出
    if (physical_rtc_time > RTC_MAX_SLEEP_SECONDS_RAW) {
        physical_rtc_time = RTC_MAX_SLEEP_SECONDS_RAW;
        printf("RTC physical time limited to maximum: %lu seconds\r\n", physical_rtc_time);
    }
    
    // 输出实际能执行的逻辑时间
    if (actual_logical_seconds != NULL) {
        *actual_logical_seconds = actual_logical_time;
    }
    
    // 调试输出
    if (logical_seconds != actual_logical_time) {
        printf("RTC correction: requested=%lu, actual_logical=%lu, physical=%lu seconds\r\n",
               logical_seconds, actual_logical_time, physical_rtc_time);
    } else {
        printf("RTC correction: logical=%lu → physical=%lu seconds (factor=%.2f)\r\n",
               actual_logical_time, physical_rtc_time, RTC_CLOCK_CORRECTION_FACTOR);
    }
    
    return physical_rtc_time;
}

int main() {
    printf("RTC修正逻辑测试\n");
    printf("最大逻辑时间: %u 秒 (%.1f 小时)\n", RTC_MAX_LOGICAL_SLEEP_SECONDS, RTC_MAX_LOGICAL_SLEEP_SECONDS / 3600.0f);
    printf("修正系数: %.2f\n\n", RTC_CLOCK_CORRECTION_FACTOR);
    
    uint32_t test_cases[] = {30, 60000, 65535, 86400}; // 30秒, 60000秒, 65535秒, 1天
    int num_cases = sizeof(test_cases) / sizeof(test_cases[0]);
    
    for (int i = 0; i < num_cases; i++) {
        uint32_t logical = test_cases[i];
        uint32_t actual_logical;
        uint32_t physical = RTC_ApplyClockCorrection(logical, &actual_logical);
        printf("测试 %d: 设定=%lu秒, 实际逻辑=%lu秒, 物理RTC=%lu秒\n\n", 
               i+1, logical, actual_logical, physical);
    }
    
    return 0;
}
