/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "gpio.h"
#include "rtc.h"
#include "adc.h"
#include "usart.h"
#include "dma.h"
#include "spi.h"
#include "i2c.h"
#include "stm32l0xx_hal_pwr.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GPS.h"
#include "rtc_sync.h"
#include "rtc_config.h"
#include "system_modules.h"
#include "GSM.h"
#include "network_command.h"

// External variables
extern uint8_t uart1_rx_buffer[];
extern uint8_t lpuart1_rx_buffer[];
extern char gps_buffer[];
extern uint16_t gps_buffer_index;
extern char gsm_ccid[];
extern int8_t gsm_signal_quality;
extern uint8_t gsm_zl_command_received;

// GSM中断相关变量已删除，改用阻塞式通信
#define GPS_BUFFER_SIZE 1024  // 增加缓冲区大小，避免溢出
#define NETWORK_CMD_BUFFER_SIZE 32

extern GPS_Data_t gps_data;
extern uint8_t gps_new_data;
extern uint8_t gps_data_ready;
extern uint32_t ADC_Value[];  // 更新为uint32_t类型
extern float pw;

// Function declarations
extern HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
extern void GPS_ParseData(void);
extern uint8_t GPS_CheckQualityTimeout(uint32_t timeout_ms);
extern void GPS_UseTempDataIfNeeded(void);
extern GPS_TempData_t gps_temp_data;

/* ADC相关宏定义 */
#define ADC_SAMPLE_COUNT            20       // ADC采样次数
#define ADC_BUFFER_SIZE             60       // ADC缓冲区大小 (3通道 × 20采样)

/* VREFINT校准相关宏定义 */
#define VREFINT_CAL_VREF            3000     // 校准时的参考电压(mV)
#define VREFINT_CAL_ADDR            ((uint16_t*)0x1FF80078)  // VREFINT校准地址
#define VREFINT_CAL_VALUE           (*VREFINT_CAL_ADDR)      // VREFINT校准值

/* MCU内部温度传感器相关宏定义 (STM32L071) - 使用工厂校准值 */

/* 电池和温度校准偏移量使用GPS.h中定义的值 */

/* ADC数据缓冲区 - 移除自定义缓冲区，使用全局ADC_Value数组 */
// static uint32_t adc_buffer[ADC_BUFFER_SIZE];  // 删除这行

/* RTC时钟修正相关全局变量 */
uint32_t last_actual_logical_seconds = 0;  // 保存最后一次实际执行的逻辑时间

/**
 * @brief  应用RTC时钟修正
 * @param  logical_seconds: 请求的逻辑休眠时间（秒）
 * @param  actual_logical_seconds: 输出实际能执行的逻辑时间（秒）
 * @retval 返回RTC应该设置的物理时间（秒）
 */
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds)
{
    uint32_t max_logical_time = RTC_MAX_LOGICAL_SLEEP_SECONDS;
    uint32_t actual_logical_time;
    uint32_t physical_rtc_time;

    // 运行时验证最大逻辑时间设置是否正确
    static uint8_t verified = 0;
    if (!verified) {
        uint32_t test_physical = (uint32_t)(RTC_MAX_LOGICAL_SLEEP_SECONDS * RTC_CLOCK_CORRECTION_FACTOR);
        if (test_physical > RTC_MAX_SLEEP_SECONDS_RAW) {
            printf("WARNING: RTC_MAX_LOGICAL_SLEEP_SECONDS too large! %lu -> %lu > %u\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        } else {
            printf("RTC correction config verified: %lu -> %lu <= %u\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        }
        verified = 1;
    }

    // 确定实际能执行的逻辑时间
    if (logical_seconds <= max_logical_time) {
        actual_logical_time = logical_seconds;
    } else {
        actual_logical_time = max_logical_time;
        printf("RTC correction limited logical time from %lu to %lu seconds\r\n",
               logical_seconds, actual_logical_time);
    }

    // 计算RTC应该设置的物理时间
    physical_rtc_time = (uint32_t)(actual_logical_time * RTC_CLOCK_CORRECTION_FACTOR);

    // 安全检查，防止溢出
    if (physical_rtc_time > RTC_MAX_SLEEP_SECONDS_RAW) {
        physical_rtc_time = RTC_MAX_SLEEP_SECONDS_RAW;
        printf("RTC physical time limited to maximum: %lu seconds\r\n", physical_rtc_time);
    }

    // 输出实际能执行的逻辑时间
    if (actual_logical_seconds != NULL) {
        *actual_logical_seconds = actual_logical_time;
    }

    // 调试输出
    if (logical_seconds != actual_logical_time) {
        printf("RTC correction: requested=%lu, actual_logical=%lu, physical=%lu seconds\r\n",
               logical_seconds, actual_logical_time, physical_rtc_time);
    } else {
        printf("RTC correction: logical=%lu -> physical=%lu seconds\r\n",
               actual_logical_time, physical_rtc_time);
    }

    return physical_rtc_time;
}

// STM32内部温度传感器计算函数
float Calculate_MCU_Temperature(uint32_t temp_adc_value);

// 模块化函数声明
// 电源管理模块
HAL_StatusTypeDef PowerModule_Init(void);
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void);
void PowerModule_EnablePeripherals(void);
void PowerModule_DisablePeripherals(void);

// GPS模块
HAL_StatusTypeDef GPSModule_Init(void);
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t unused_timeout, uint8_t is_first_boot);
void GPSModule_PowerOn(void);
void GPSModule_PowerOff(void);
void GPSModule_ClearData(void);
HAL_StatusTypeDef GPSModule_SyncRTC(void);

// 传感器模块
HAL_StatusTypeDef SensorModule_Init(void);
HAL_StatusTypeDef SensorModule_ReadData(void);

// 数据处理模块
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size);
void DataModule_PrintData(const char *data_string); // 替代GSM发送的打印输出

// 系统控制模块
void SystemModule_PrintCurrentTime(void);
void SystemModule_EnterSleepMode(void);

// 电池保护模块
uint8_t BatteryProtection_CheckVoltage(void);
void BatteryProtection_EnterLowVoltageMode(void);
void BatteryProtection_ExitLowVoltageMode(void);

// 时间管理函数
uint32_t GetCurrentWakeupTime(void);

// 全局变量：保存设备唤醒后的RTC时间
RTC_TimeTypeDef current_wakeup_time;
RTC_DateTypeDef current_wakeup_date;
uint8_t rtc_time_valid = 0;  // RTC时间有效标志：0=无效(默认时间), 1=有效(已同步)

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// 休眠配置
#define SLEEP_DURATION_SECONDS 30       // 休眠时间设置
#define UART1_RX_BUFFER_SIZE 256        // UART接收缓冲区大小

// 历史数据发送配置
#define HISTORICAL_DATA_SEND_LIMIT 100    // 每次发送历史数据的条数限制
#define HISTORICAL_DATA_BATCH_SIZE 8      // 批量发送最大条数 最大8条
#define BATCH_SEPARATOR "+M+"             // 数据分隔符
#define BATCH_BUFFER_SIZE 2048            // 批量数据缓冲区大小

// 静态全局缓冲区，避免栈溢出
static char g_batch_data_buffer[BATCH_BUFFER_SIZE];
static uint8_t g_read_data_buffer[SPI_FLASH_RECORD_SIZE];

// 电池电压保护阈值
#define BATTERY_LOW_VOLTAGE_THRESHOLD    3.4f    // 低电压保护阈值（V）
#define BATTERY_RECOVERY_VOLTAGE_THRESHOLD 3.6f  // 电压恢复阈值（V）

/**
 * @brief 批量读取历史数据并拼接（使用全局缓冲区优化）
 * @param batch_buffer 拼接后的数据缓冲区
 * @param buffer_size 缓冲区大小
 * @param actual_count 实际读取的记录数
 * @return HAL_StatusTypeDef 执行状态
 */
HAL_StatusTypeDef BatchHistoricalData(char* batch_buffer, uint32_t buffer_size, uint32_t* actual_count)
{
    if (batch_buffer == NULL || actual_count == NULL || buffer_size == 0) {
        return HAL_ERROR;
    }

    uint32_t count = 0;
    uint32_t total_length = 0;
    // 使用全局缓冲区，避免栈溢出

    // 清空缓冲区
    memset(batch_buffer, 0, buffer_size);
    *actual_count = 0;

    // 批量读取数据
    while (count < HISTORICAL_DATA_BATCH_SIZE && SPI_FLASH_GetRecordCount() > 0) {
        uint16_t data_size = SPI_FLASH_RECORD_SIZE;

        // 读取数据但不标记为已读
        if (SPI_FLASH_ReadRecordEx(0, g_read_data_buffer, &data_size, 0) == HAL_OK) {
            // 确保数据以null结尾
            g_read_data_buffer[SPI_FLASH_RECORD_SIZE - 1] = '\0';

            // 检查数据是否有效
            if (strlen((char*)g_read_data_buffer) > 0 && g_read_data_buffer[0] != 0xFF) {
                uint32_t data_len = strlen((char*)g_read_data_buffer);
                uint32_t separator_len = (count > 0) ? strlen(BATCH_SEPARATOR) : 0;

                // 检查缓冲区空间是否足够
                if (total_length + data_len + separator_len >= buffer_size - 1) {
                    printf("Batch buffer full, stopping at %lu records\r\n", count);
                    break;
                }

                // 添加分隔符（除了第一条数据）
                if (count > 0) {
                    strcat(batch_buffer, BATCH_SEPARATOR);
                    total_length += separator_len;
                }

                // 添加数据
                strcat(batch_buffer, (char*)g_read_data_buffer);
                total_length += data_len;
                count++;

                printf("Batched record %lu, length: %lu\r\n", count, data_len);

                // 立即标记为已读，这样下次读取索引0时就是下一条数据
                SPI_FLASH_MarkMultipleAsRead(1);
            } else {
                printf("Invalid data found, marking as read and skipping\r\n");
                // 无效数据直接标记为已读并跳过，不计入批量数据
                SPI_FLASH_MarkMultipleAsRead(1);
                // 不增加count，继续下一条记录
            }
        } else {
            printf("Failed to read historical data\r\n");
            break;
        }
    }

    *actual_count = count;

    if (count > 0) {
        printf("Batch completed: %lu records, total length: %lu bytes\r\n", count, total_length);
        return HAL_OK;
    } else {
        return HAL_ERROR;
    }
}

// 电压监测测试配置
#define ENABLE_VOLTAGE_MONITOR_TEST 0   // 1=启用电压监测测试, 0=禁用
#define VOLTAGE_MONITOR_DURATION 60     // 电压监测持续时间（秒）

#define GPS_TIMEOUT_NORMAL 120        // GPS常规定位等待时间（秒）
#define GPS_TIMEOUT_FIRST_BOOT 300    // GPS初次启动等待时间（秒）

// GPS调试信息控制宏（0=禁用，1=启用）
#define GPS_DEBUG_ENABLE 0

// AB指令测试宏（0=使用真实GPS速度，1=使用虚拟速度）
#define AB_COMMAND_TEST_ENABLE 1

// 虚拟速度值（用于AB指令测试，单位：节 knots）
#define VIRTUAL_SPEED_VALUE 9.0f  // 可以修改这个值来测试不同速度

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
volatile uint8_t rtcWakeupFlag = 0;  // RTC唤醒标志
extern uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE]; // UART接收缓冲区

// LSM6DS3传感器数据结构 - 在main.c中定义
extern LSM6DS3_Data imuData;        // IMU传感器数据
extern LSM6DS3_Attitude attitude;   // 传感器融合后的姿态数据

// 动态休眠时间控制变量
uint32_t wakeup_counter = 0;        // 动态唤醒计数器，0表示使用默认值

// 全局首次启动标志
uint8_t is_first_boot = 1;          // 首次启动标志，用于强制RTC同步

// 电池电压保护状态标志
uint8_t low_voltage_protection_active = 0;  // 低电压保护状态：0=正常模式，1=保护模式

// GPS和GSM同步标志位
volatile uint8_t gps_task_completed = 0;    // GPS任务完成标志：0=未完成，1=已完成
volatile uint8_t gsm_can_start = 0;         // GSM可以启动标志：0=不可启动，1=可以启动

// RTOS通信对象
// 队列句柄
osMessageQId gpsDataQueueHandle;
osMessageQId sensorDataQueueHandle;

// 信号量句柄
osSemaphoreId gpsReadySemHandle;
osSemaphoreId sensorReadySemHandle;
osSemaphoreId dataSentSemHandle;
osSemaphoreId gsmReadySemHandle;

// 事件组句柄 - 使用信号量模拟事件组功能
osSemaphoreId gpsStartSemHandle;
osSemaphoreId sensorStartSemHandle;
osSemaphoreId gsmStartSemHandle;

// 事件组标志定义
#define GPS_READY_BIT       (1UL << 0)
#define SENSOR_READY_BIT    (1UL << 1)
#define DATA_SENT_BIT       (1UL << 2)
#define SLEEP_READY_BIT     (1UL << 3)

// 数据结构定义在system_modules.h中
/* USER CODE END Variables */
osThreadId GPSTaskHandle;
uint32_t GPSTaskBuffer[ 384 ];
osStaticThreadDef_t GPSTaskControlBlock;
osThreadId AccelTaskHandle;
uint32_t AccelTaskBuffer[ 256 ];
osStaticThreadDef_t AccelTaskControlBlock;
osThreadId FlashTaskHandle;
uint32_t FlashTaskBuffer[ 128 ];
osStaticThreadDef_t FlashTaskControlBlock;
osThreadId myPowerTaskHandle;
uint32_t myPowerTaskBuffer[ 768 ];  // 增加主任务栈，用于批量数据处理
osStaticThreadDef_t myPowerTaskControlBlock;
osThreadId GSMTaskHandle;
uint32_t GSMTaskBuffer[ 384 ];
osStaticThreadDef_t GSMTaskControlBlock;

// 设备唤醒计数管理
static unsigned long device_wakeup_count = 0;  // 设备唤醒次数

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void EnterStopMode(void);
void BlinkLEDs(uint8_t count, uint32_t delay);
void SystemClock_Config(void);  // 系统时钟配置函数在main.c中定义
/* USER CODE END FunctionPrototypes */

void StartGPSTask(void const * argument);
void StartAccelTask(void const * argument);
void StartFlashTask(void const * argument);
void StartPowerTask(void const * argument);
void StartGSMTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  // 创建数据就绪信号量
  osSemaphoreDef(gpsReadySem);
  gpsReadySemHandle = osSemaphoreCreate(osSemaphore(gpsReadySem), 1);

  osSemaphoreDef(sensorReadySem);
  sensorReadySemHandle = osSemaphoreCreate(osSemaphore(sensorReadySem), 1);

  osSemaphoreDef(dataSentSem);
  dataSentSemHandle = osSemaphoreCreate(osSemaphore(dataSentSem), 1);

  osSemaphoreDef(gsmReadySem);
  gsmReadySemHandle = osSemaphoreCreate(osSemaphore(gsmReadySem), 1);

  // 创建任务启动信号量
  osSemaphoreDef(gpsStartSem);
  gpsStartSemHandle = osSemaphoreCreate(osSemaphore(gpsStartSem), 1);

  osSemaphoreDef(sensorStartSem);
  sensorStartSemHandle = osSemaphoreCreate(osSemaphore(sensorStartSem), 1);

  osSemaphoreDef(gsmStartSem);
  gsmStartSemHandle = osSemaphoreCreate(osSemaphore(gsmStartSem), 1);

  // 初始状态：获取数据就绪信号量，等待任务释放
  osSemaphoreWait(gpsReadySemHandle, 0);
  osSemaphoreWait(sensorReadySemHandle, 0);
  osSemaphoreWait(dataSentSemHandle, 0);
  osSemaphoreWait(gsmReadySemHandle, 0);

  // 初始状态：获取启动信号量，等待主任务释放
  osSemaphoreWait(gpsStartSemHandle, 0);
  osSemaphoreWait(sensorStartSemHandle, 0);
  osSemaphoreWait(gsmStartSemHandle, 0);
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  // 创建消息队列
  osMessageQDef(gpsDataQueue, 2, GPSQueueData_t);
  gpsDataQueueHandle = osMessageCreate(osMessageQ(gpsDataQueue), NULL);

  osMessageQDef(sensorDataQueue, 2, SensorQueueData_t);
  sensorDataQueueHandle = osMessageCreate(osMessageQ(sensorDataQueue), NULL);
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of GPSTask */
  osThreadStaticDef(GPSTask, StartGPSTask, osPriorityHigh, 0, 384, GPSTaskBuffer, &GPSTaskControlBlock);
  GPSTaskHandle = osThreadCreate(osThread(GPSTask), NULL);

  /* definition and creation of AccelTask */
  osThreadStaticDef(AccelTask, StartAccelTask, osPriorityIdle, 0, 256, AccelTaskBuffer, &AccelTaskControlBlock);
  AccelTaskHandle = osThreadCreate(osThread(AccelTask), NULL);

  /* definition and creation of FlashTask */
  osThreadStaticDef(FlashTask, StartFlashTask, osPriorityIdle, 0, 128, FlashTaskBuffer, &FlashTaskControlBlock);
  FlashTaskHandle = osThreadCreate(osThread(FlashTask), NULL);

  /* definition and creation of myPowerTask */
  osThreadStaticDef(myPowerTask, StartPowerTask, osPriorityIdle, 0, 768, myPowerTaskBuffer, &myPowerTaskControlBlock);
  myPowerTaskHandle = osThreadCreate(osThread(myPowerTask), NULL);

  /* definition and creation of GSMTask */
  osThreadStaticDef(GSMTask, StartGSMTask, osPriorityHigh, 0, 384, GSMTaskBuffer, &GSMTaskControlBlock);
  GSMTaskHandle = osThreadCreate(osThread(GSMTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  printf("RTOS OK\r\n");
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartGPSTask */
/**
  * @brief  GPS任务实现
  * @param  argument: 不使用
  * @retval None
  */
/* USER CODE END Header_StartGPSTask */
void StartGPSTask(void const * argument)
{
  /* USER CODE BEGIN StartGPSTask */

  /* 无限循环 */
  for(;;)
  {
    // 等待主任务启动信号
    if (osSemaphoreWait(gpsStartSemHandle, osWaitForever) == osOK) {
      // 执行GPS数据采集
      GPSModule_PowerOn();
      HAL_StatusTypeDef gps_result = GPSModule_WaitForData(0, is_first_boot); // 超时时间由函数内部根据is_first_boot决定

      // GPS时钟同步逻辑优化（在关闭GPS电源前）
      if (gps_result == HAL_OK) {
        // 等待GPS数据完全稳定
        osDelay(1000);

        // 执行RTC同步
        GPSModule_SyncRTC();

        // GPS同步后重新检查工作时间段
        uint8_t post_gps_work_check = NetworkCommand_CheckWorkTime();
        if (post_gps_work_check != 1) {
            // 不在工作时间段内，跳过GSM和数据处理，直接休眠
            printf("Post-GPS check: Not in work hours, skipping GSM and data processing\r\n");
            SystemModule_EnterSleepMode();
            continue;
        }
        printf("Post-GPS check: In work hours, continuing normal operation\r\n");
      }

      // 无论GPS成功与否，第一次尝试后都清除首次启动标志
      if (is_first_boot) {
        is_first_boot = 0;
      }

      // 确保GPS任务完成后LED关闭（增强LED控制稳定性）
      LED1_OFF;

      // RTC同步后关闭GPS电源以节省功耗
      GPSModule_PowerOff();

      // 设置GPS任务完成标志
      gps_task_completed = 1;
      printf("GPS task completed, power off\r\n");

      // 释放GPS数据就绪信号量
      osSemaphoreRelease(gpsReadySemHandle);
    }

    // 短暂延时避免过度占用CPU
    osDelay(10);
  }
  /* USER CODE END StartGPSTask */
}

/* USER CODE BEGIN Header_StartAccelTask */
/**
* @brief 加速度传感器任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartAccelTask */
void StartAccelTask(void const * argument)
{
  /* USER CODE BEGIN StartAccelTask */

  /* 无限循环 */
  for(;;)
  {
    // 等待主任务启动信号
    if (osSemaphoreWait(sensorStartSemHandle, osWaitForever) == osOK) {
      // 执行传感器数据读取
      osDelay(1000);  // 额外等待1秒，确保V_OUT_ON电源完全稳定
      SensorModule_Init();
      SensorModule_ReadData();

      // 释放传感器数据就绪信号量
      osSemaphoreRelease(sensorReadySemHandle);
    }

    // 短暂延时避免过度占用CPU
    osDelay(10);
  }
  /* USER CODE END StartAccelTask */
}

/* USER CODE BEGIN Header_StartGSMTask */
/**
* @brief GSM任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartGSMTask */
void StartGSMTask(void const * argument)
{
  /* USER CODE BEGIN StartGSMTask */
  /* 无限循环 */
  for(;;)
  {
    // 等待主任务启动信号
    if (osSemaphoreWait(gsmStartSemHandle, osWaitForever) == osOK) {
      // 等待GPS任务完全结束后才开始GSM初始化
      while (!gsm_can_start) {
        osDelay(100);  // 每100ms检查一次
      }

      // 开始GSM初始化

      // 执行GSM阻塞式初始化流程（包含服务器连接）
      GSM_Status_t gsm_status = GSM_SimpleInit();

      // GSM初始化完成

      // 释放GSM就绪信号量
      osSemaphoreRelease(gsmReadySemHandle);
    }

    // 短暂延时避免过度占用CPU
    osDelay(10);
  }
  /* USER CODE END StartGSMTask */
}

/* USER CODE BEGIN Header_StartFlashTask */
/**
* @brief Flash任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartFlashTask */
void StartFlashTask(void const * argument)
{
  /* USER CODE BEGIN StartFlashTask */
  /* 无限循环 */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartFlashTask */
}

/* USER CODE BEGIN Header_StartPowerTask */
/**
* @brief 主电源控制任务实现
* @param argument: 不使用
* @retval None
*/
/* USER CODE END Header_StartPowerTask */
void StartPowerTask(void const * argument)
{
  /* USER CODE BEGIN StartPowerTask */
  unsigned long cycle_count = 0;

  printf("Main Task OK\r\n");

  // 设备启动时从FLASH读取设置
  NetworkCommand_LoadSettingsOnBoot();

  // 显示启动时的历史数据数量
  printf("Historical Data: %d\r\n", SPI_FLASH_GetRecordCount());

  /* 无限循环 */
  for(;;)
  {
    cycle_count++;

    printf("===== Cycle #%lu (Wakeup #%lu, Time: %lu min) =====\r\n",
           cycle_count, device_wakeup_count, GetCurrentWakeupTime());

    // 重置信号量状态，确保每个周期的同步正确
    // 清除任何挂起的启动信号量（防止残留在释放状态）
    osSemaphoreWait(gpsStartSemHandle, 0);
    osSemaphoreWait(sensorStartSemHandle, 0);
    osSemaphoreWait(gsmStartSemHandle, 0);

    // 清除任何挂起的就绪信号量（防止残留在释放状态）
    osSemaphoreWait(gpsReadySemHandle, 0);
    osSemaphoreWait(sensorReadySemHandle, 0);
    osSemaphoreWait(gsmReadySemHandle, 0);

    // 重置GPS和GSM同步标志位
    gps_task_completed = 0;
    gsm_can_start = 0;

    // 1. 唤醒后立即打印RTC时间
    SystemModule_PrintCurrentTime();

    // 2. 电源管理模块初始化
    PowerModule_Init();

    // 3. Check battery voltage
    PowerModule_ReadBatteryVoltage();

    // 电压监测测试模式 - 只在初次上电时有效
    #if ENABLE_VOLTAGE_MONITOR_TEST
    if (is_first_boot) {
//        printf("电压监测测试模式启动，持续时间: %d 秒\r\n", VOLTAGE_MONITOR_DURATION);

        uint32_t monitor_start_time = HAL_GetTick();
        uint32_t last_voltage_print = monitor_start_time;
        uint32_t print_interval = 1000; // 每0.5秒打印一次电压

        while ((HAL_GetTick() - monitor_start_time) < (VOLTAGE_MONITOR_DURATION * 1000)) {
            // 每0.5秒读取并打印一次电压
            if ((HAL_GetTick() - last_voltage_print) >= print_interval) {
                last_voltage_print = HAL_GetTick();

                // 重新读取电压
                PowerModule_ReadBatteryVoltage();

                // 简洁输出格式
                printf("Battery: %.3f V\r\n", pw);
            }

            // 短暂延时
            osDelay(50);
        }

        printf("电压监测测试完成\r\n");

        // 电压监测完成后直接进入休眠，跳过其他功能
        SystemModule_EnterSleepMode();
        continue;
    }
    #endif

    // 4. 长时间休眠跟踪检查（在电压检测之前）
    if (NetworkCommand_CheckLongSleepStatus()) {
        // 需要继续休眠，直接进入休眠模式
        printf("Long sleep tracking: continuing sleep cycle\r\n");
        SystemModule_EnterSleepMode();
        continue; // 跳过本次循环的其他处理
    }

    // 5. Battery voltage protection check
    uint8_t voltage_protection_result = BatteryProtection_CheckVoltage();
    if (voltage_protection_result == 1) {
        // Enter low voltage protection mode, skip all data collection work
        printf("Low voltage protection active - skipping data collection\r\n");
        SystemModule_EnterSleepMode();
        continue; // Skip the rest of this loop
    } else if (voltage_protection_result == 2) {
        // Voltage recovered, exit low voltage protection mode
        printf("Voltage recovered - resuming normal operation\r\n");
    }

    // 6. 只有在非低电压保护模式下才增加设备唤醒计数
    device_wakeup_count++;

    // 7. 检查工作时间段设置
    uint8_t work_time_result = NetworkCommand_CheckWorkTime();
    if (work_time_result == 2) {
        // RTC未同步，无法判断工作时间段，继续正常运行
        printf("RTC not synced, continuing normal operation\r\n");
    } else if (work_time_result == 4) {
        // 工作时间段被禁用（开始时间==结束时间），继续正常运行
        printf("Work time feature disabled, continuing normal operation\r\n");
    } else if (work_time_result == 0) {
        // 未设置工作时间段或不在工作时间段内
        // 如果返回0且已设置工作时间段，说明不在工作时间段内，应该进入休眠
        NetworkCommand_Result_t work_time_check;
        if (NetworkCommand_LoadFromFlash(CMD_TYPE_WORK_TIME, &work_time_check) == HAL_OK && work_time_check.is_valid) {
            // 已设置工作时间段但当前不在工作时间内，进入休眠
            printf("Outside work hours - entering sleep mode\r\n");
            SystemModule_EnterSleepMode();
            continue; // Skip the rest of this loop
        }
        // 未设置工作时间段，继续正常运行
        printf("Work time not configured, continuing normal operation\r\n");
    } else if (work_time_result == 3) {
        // 已计算智能休眠时间，直接进入休眠
        printf("Smart sleep calculated - entering sleep mode until work time\r\n");
        SystemModule_EnterSleepMode();
        continue; // Skip the rest of this loop
    } else {
        // 在工作时间段内，继续正常运行
        printf("Within work hours, continuing normal operation\r\n");
    }

    // 8. 首次启动时强制获取CCID（无论是否跳过GSM）
    if (is_first_boot && strcmp(gsm_ccid, "TEST0001") == 0) {
      printf("First boot - attempting to get CCID...\r\n");

      // 临时启动GSM模块获取CCID
      GSM_PowerOn();
      osDelay(3000);  // 等待模块启动

      if (GSM_Init() == GSM_OK) {
        char temp_ccid[32];
        if (GSM_GetCCID(temp_ccid) == GSM_OK) {
          strcpy(gsm_ccid, temp_ccid);
          printf("CCID obtained: %s\r\n", gsm_ccid);
        } else {
          printf("Failed to get CCID, using default\r\n");
        }

        // 获取信号强度
        uint8_t signal;
        if (GSM_GetSignal(&signal) == GSM_OK) {
          gsm_signal_quality = (int8_t)signal;
          printf("Signal quality: %d\r\n", gsm_signal_quality);
        }
      } else {
        printf("GSM init failed, using default CCID\r\n");
      }

      // 关闭GSM模块
      GSM_PowerOff();
      osDelay(1000);
    }

    // 9. 检查N指令（存储N条数据后再启动GSM模块）
    uint8_t skip_gsm_this_cycle = NetworkCommand_ShouldSkipGSM();
    if (skip_gsm_this_cycle) {
        printf("Skip GSM this cycle - N command active\r\n");
    }

    // 10. 先启动GPS和传感器任务（并行执行）
    osSemaphoreRelease(gpsStartSemHandle);
    osSemaphoreRelease(sensorStartSemHandle);

    // 11. 等待GPS和传感器数据就绪
    uint8_t gps_ok = 0, gsm_ok = 0;
    // 根据是否首次启动选择合适的GPS超时时间
    uint32_t gps_timeout = is_first_boot ? (GPS_TIMEOUT_FIRST_BOOT + 10) * 1000 : (GPS_TIMEOUT_NORMAL + 10) * 1000;
    if (osSemaphoreWait(gpsReadySemHandle, gps_timeout) == osOK) {
      gps_ok = 1;
    }
    // 等待传感器数据就绪（传感器数据已在任务中处理和显示）
    osSemaphoreWait(sensorReadySemHandle, osWaitForever);

    // 12. 根据N指令决定是否启动GSM任务
    if (!skip_gsm_this_cycle) {
        gsm_can_start = 1;
//        printf("GPS task completed, starting GSM task\r\n");
        osSemaphoreRelease(gsmStartSemHandle);

        // 13. 等待GSM数据就绪
        if (osSemaphoreWait(gsmReadySemHandle, osWaitForever) == osOK) {
          gsm_ok = 1;
        }
    } else {
        printf("GSM task skipped due to N command\r\n");
        gsm_ok = 0;  // 标记GSM未启动
    }

    // Print data collection results
    if (gps_ok && gps_data.valid) {
//      printf("GPS: %d satellites, HDOP=%.1f\r\n",
//             gps_data.satellites, gps_data.hdop);
    }

    // 传感器数据已在SensorModule_ReadData中显示

    // 14. 数据合成和输出
    char data_string_s[300];  // S数据（用于发送）
    char data_string_b[300];  // B数据（用于存储）
    if (DataModule_CreatePacket(data_string_s, sizeof(data_string_s)) == HAL_OK) {
      // 生成B数据（将S替换为B）
      strcpy(data_string_b, data_string_s);
      char *s_pos = strchr(data_string_b, 'S');
      if (s_pos != NULL) {
        *s_pos = 'B';
      }
      uint8_t data_sent_successfully = 0;

      // 如果N指令启用且跳过GSM，直接存储数据
      if (skip_gsm_this_cycle) {
        printf("N command active - storing data directly to SPI Flash\r\n");
        data_sent_successfully = 0;  // 强制存储到Flash
      }
      // GSM数据发送（如果GSM初始化成功且未跳过）
      else if (gsm_ok && GSM_GetState() == GSM_STATE_CONNECTED) {

        // GSM输出缓冲区处理已删除，改用阻塞式通信
        GSM_Status_t send_result = GSM_SendData(data_string_s, strlen(data_string_s));
        if (send_result == GSM_OK) {
          printf("Real-time data sent successfully\r\n");
          data_sent_successfully = 1;

          // 立即打印实时数据内容（S数据）
          DataModule_PrintData(data_string_s);

          // 实时数据发送成功后，检查并批量发送历史数据
          uint32_t historical_count = SPI_FLASH_GetRecordCount();
          if (historical_count > 0) {
            printf("Starting batch historical data sending, total records: %lu\r\n", historical_count);

            uint32_t sent_batches = 0;
            uint32_t max_batches = (HISTORICAL_DATA_SEND_LIMIT + HISTORICAL_DATA_BATCH_SIZE - 1) / HISTORICAL_DATA_BATCH_SIZE;

            while (sent_batches < max_batches && SPI_FLASH_GetRecordCount() > 0) {
              uint32_t batch_count = 0;

              // 批量读取并拼接数据（使用全局缓冲区）
              if (BatchHistoricalData(g_batch_data_buffer, BATCH_BUFFER_SIZE, &batch_count) == HAL_OK) {
                printf("Sending batch %lu with %lu records, data length: %lu bytes\r\n",
                       sent_batches + 1, batch_count, strlen(g_batch_data_buffer));

                // 发送批量数据
                GSM_Status_t batch_send_result = GSM_SendData(g_batch_data_buffer, strlen(g_batch_data_buffer));
                if (batch_send_result == GSM_OK) {
                  // ZL指令已收到，批量发送成功
                  printf("Batch %lu sent successfully (%lu records)\r\n",
                         sent_batches + 1, batch_count);
                  sent_batches++;
                } else {
                  // 未收到ZL指令，批量发送失败，需要恢复数据
                  printf("Batch %lu send failed, need to restore %lu records\r\n",
                         sent_batches + 1, batch_count);

                  // TODO: 这里需要实现数据恢复逻辑，将已读的数据重新标记为未读
                  // 目前暂时停止发送，避免数据丢失
                  break;
                }
              } else {
                printf("Failed to batch historical data\r\n");
                break;
              }
            }

            // 显示当前历史数据数量
            printf("Historical Data: %d\r\n", SPI_FLASH_GetRecordCount());
          }

          // 数据发送成功后，检查速度阈值功能
          if (gps_data.valid) {
            // 获取当前速度值（使用原始GPS速度，单位：节）
            float current_speed = gps_data.speed;

            #if AB_COMMAND_TEST_ENABLE
            // 测试模式：使用虚拟速度值
            current_speed = VIRTUAL_SPEED_VALUE;
            printf("AB Test Mode: Using virtual speed %.1f knots (Real GPS: %.1f knots)\r\n",
                   current_speed, gps_data.speed);
            #endif

            NetworkCommand_CheckSpeedThreshold(current_speed);
          }
        } else {
          printf("Real-time data send failed\r\n");
          data_sent_successfully = 0;
        }
      } else {
        printf("GSM not connected, cannot send data\r\n");
        data_sent_successfully = 0;
      }

      // 数据发送完成后关闭TCP连接
      if (gsm_ok && GSM_GetState() == GSM_STATE_CONNECTED) {
        GSM_CloseServer();
      }

      // 只有在数据发送失败时才缓存到SPI Flash
      if (!data_sent_successfully) {
        uint8_t writeData[SPI_FLASH_RECORD_SIZE];
        memset(writeData, 0, SPI_FLASH_RECORD_SIZE);  // 用0填充，而不是0xFF

        // Ensure data does not exceed record size limit
        size_t data_len = strlen(data_string_b);
        if (data_len >= SPI_FLASH_RECORD_SIZE) {
          printf("Data too long, exceeds record size limit\r\n");
        } else {
          // Copy B data (包含字符串结束符)
          strcpy((char *)writeData, data_string_b);

          // Write to SPI Flash ring buffer - 传入完整记录大小
          if (SPI_FLASH_WriteRecord(writeData, SPI_FLASH_RECORD_SIZE) == HAL_OK) {
            printf("Data cached to SPI Flash (send failed, Unread: %d)\r\n", SPI_FLASH_GetRecordCount());
          } else {
            printf("SPI Flash write failed, data lost!\r\n");
          }
        }
      }

      // 如果数据发送失败，打印B数据内容
      if (!data_sent_successfully) {
        // 发送失败，打印B数据（S数据已在发送成功时打印）
        DataModule_PrintData(data_string_b);
      }

    } else {
      printf("Data synthesis failed\r\n");
    }

    // 数据合成和处理完成后清除GPS数据，避免重复使用
    GPSModule_ClearData();

    // 15. Enter sleep mode
    SystemModule_EnterSleepMode();

    printf("Cycle #%lu completed\r\n\n", cycle_count);

    // 注意：is_first_boot标志在GPS任务成功RTC同步后被清除
  }
  /* USER CODE END StartPowerTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

// LED交替闪烁函数
void BlinkLEDs(uint8_t count, uint32_t delay)
{
  for (uint8_t i = 0; i < count; i++)
  {
    LED1_ON;
    osDelay(delay);

    LED1_OFF;
    osDelay(delay);
  }

  // 最后关闭所有LED
  LED1_OFF;
}

// 进入STOP模式 - 超精简版本
void EnterStopMode(void)
{
  // 关闭LED
  LED1_OFF;

  // Turn off all peripheral power to save energy
  GPS_PWR_OFF;  // Turn off GPS power using correct macro
  V_OUT_OFF;    // Turn off sensor power using macro
  RF_PWR_OFF;   // Turn off GSM power using macro
  CAM_PW_OFF;   // Turn off camera power using macro
  VCHK_OFF;     // Turn off ADC sampling switch

  // 停止UART接收中断
  HAL_UART_AbortReceive_IT(&huart1);
  HAL_UART_AbortReceive_IT(&hlpuart1);

  // 禁用UART中断
  HAL_NVIC_DisableIRQ(USART1_IRQn);
  HAL_NVIC_DisableIRQ(LPUART1_IRQn);

  // 关闭所有外设时钟
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_USART1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();

  // 确保UART传输完成
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  while(__HAL_UART_GET_FLAG(&hlpuart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);  // 使用HAL_Delay而不是osDelay，因为即将暂停调度器

  // 配置RTC唤醒定时器

  // 确保所有中断被处理
  __disable_irq();

  // 取消激活任何现有的唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // 清除任何挂起的标志
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // 设置唤醒定时器，使用RTC_WAKEUPCLOCK_CK_SPRE_16BITS（1Hz时钟源）
  // 使用外部变量或默认值
  // 休眠时间 = 计数器 / 1Hz，所以计数器 = 休眠秒数 - 1
  uint32_t default_wakeup_counter = SLEEP_DURATION_SECONDS - 1;
  uint32_t logical_wakeup_counter = (wakeup_counter > 0) ? wakeup_counter : default_wakeup_counter;

  // 应用RTC时钟修正
  uint32_t actual_logical_seconds;
  uint32_t physical_rtc_counter = RTC_ApplyClockCorrection(logical_wakeup_counter, &actual_logical_seconds);

  // 保存实际执行的逻辑时间到全局变量
  last_actual_logical_seconds = actual_logical_seconds;

  // 设置RTC唤醒计数器（使用修正后的物理时间）
  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, physical_rtc_counter, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    __enable_irq();
    return;
  }

  // 启用RTC中断，使用最高优先级
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // 启用RTC唤醒的EXTI
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();

  __enable_irq();

  // 获取当前时间
  RTC_TimeTypeDef time_before;
  RTC_DateTypeDef date_before;
  HAL_RTC_GetTime(&hrtc, &time_before, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_before, RTC_FORMAT_BIN);

  printf("Time before sleep: %02d:%02d:%02d\r\n",
         time_before.Hours, time_before.Minutes, time_before.Seconds);

  // 确保消息发送完毕
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);  // 使用HAL_Delay而不是osDelay，因为即将禁用中断

  // 禁用所有中断，准备进入低功耗模式
  __disable_irq();

  // 禁用SysTick中断
  SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

  // 禁用所有中断，只保留RTC唤醒中断
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICER[i] = 0xFFFFFFFF;
  }

  // 确保RTC唤醒中断被启用
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // 清除所有挂起的中断
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICPR[i] = 0xFFFFFFFF;
  }

  // 暂停调度器
  vTaskSuspendAll();

  // 重新启用全局中断，但只有RTC中断处于活动状态
  __enable_irq();

  // 进入STOP模式，使用低功耗调节器
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

  // 唤醒后 - 以下代码在唤醒后执行

  // 打开LED表示唤醒开始
  LED1_ON;

  // 重新配置系统时钟 - STOP模式后必需
  SystemClock_Config();

  // 重新初始化所有必要的外设时钟
  __HAL_RCC_USART1_CLK_ENABLE();
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_I2C1_CLK_ENABLE();
  __HAL_RCC_DMA1_CLK_ENABLE();

  // 重新初始化UART1和LPUART1
  MX_USART1_UART_Init();
  MX_LPUART1_UART_Init();

  // 重新启用UART中断
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);

  // 重新启动UART接收中断
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  // LPUART1使用阻塞模式，不需要中断接收

  // 重新启用SysTick中断
  SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

  // 重新启用全局中断
  __enable_irq();

  // 恢复调度器（必须在使用osDelay之前）
  xTaskResumeAll();

  // LED闪烁表示正在唤醒
  LED1_OFF;
  osDelay(100);
  LED1_ON;

  // 获取当前时间
  RTC_TimeTypeDef time_after;
  RTC_DateTypeDef date_after;
  HAL_RTC_GetTime(&hrtc, &time_after, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_after, RTC_FORMAT_BIN);

  // 停止唤醒定时器
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  printf("Woke up from sleep mode\r\n");
  printf("Time after sleep: %02d:%02d:%02d\r\n",
         time_after.Hours, time_after.Minutes, time_after.Seconds);

  // 计算经过的时间
  int elapsed_seconds = (time_after.Hours - time_before.Hours) * 3600 +
                        (time_after.Minutes - time_before.Minutes) * 60 +
                        (time_after.Seconds - time_before.Seconds);
  if (elapsed_seconds < 0) {
    elapsed_seconds += 24 * 3600; // 处理跨天情况
  }

  printf("Elapsed time: %d seconds\r\n", elapsed_seconds);

  // 重新初始化电源引脚为输出模式
  PowerPins_InitForWakeup();

  // 重新启用外设（但不包括GPS电源，GPS电源由GPS任务控制）
  // PowerModule_EnablePeripherals();  // 注释掉，避免在休眠后立即开启GPS电源

  // 只开启必要的外设电源
  V_OUT_ON;  // 传感器电源
  osDelay(500);  // 等待传感器电源稳定，确保LSM6DS3和SPI Flash正常工作

  // 清除GPS缓冲区
  memset(gps_buffer, 0, GPS_BUFFER_SIZE);
  gps_buffer_index = 0;

  // 确保UART接收中断已启动
  if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
      // 如果UART接收未启动，重新启动
      HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }
  // LPUART1使用阻塞模式，不需要中断接收

  // 确保I2C已初始化
  if (hi2c1.State == HAL_I2C_STATE_RESET) {
      MX_I2C1_Init();
  }
}

// RTC唤醒定时器事件回调
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
  // 设置唤醒标志
  rtcWakeupFlag = 1;

  // 清除RTC唤醒标志
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);

  // 清除EXTI线标志
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // 注意：这里不要使用printf，因为从中断上下文调用可能导致系统崩溃
}

// 模块化函数实现

// Power management module initialization
HAL_StatusTypeDef PowerModule_Init(void)
{
    // Turn on power switches
    V_OUT_ON; // Turn on 3-axis and SPI_FLASH power
    VCHK_ON;  // Turn on ADC sampling switch
    osDelay(2000); // Wait for stabilization

    return HAL_OK;
}

// 电池电压读取函数
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[60];  // 3通道×20采样=60个数据

    // 清空ADC数据数组
    memset(ADC_Value, 0, sizeof(ADC_Value));

    // 开启ADC采样开关
    VCHK_ON;
    osDelay(10);  // 等待开关稳定

    float voltage = 0.0f;

    // 强制ADC重置和重新初始化
    HAL_ADC_DeInit(&hadc);
    osDelay(10);
    MX_ADC_Init();
    osDelay(10);

    // 校准ADC
    HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED);

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)ADC_Value, 60) == HAL_OK) {
        osDelay(100);

        // ADC数据采集完成

        // 分离三个通道的数据
        // 正确的通道顺序：CH6(电压) -> VREFINT(参考电压) -> TEMP(温度)
        uint32_t battery_adc_sum = 0;
        uint32_t temp_adc_sum = 0;
        uint32_t vrefint_adc_sum = 0;
        uint32_t battery_count = 0;
        uint32_t temp_count = 0;
        uint32_t vrefint_count = 0;

        for (int i = 0; i < 60; i += 3) {
            // CH6 (电池电压) - 索引 0, 3, 6, 9...
            battery_adc_sum += ADC_Value[i];
            battery_count++;

            // VREFINT (内部参考电压) - 索引 1, 4, 7, 10...
            if (i + 1 < 60) {
                vrefint_adc_sum += ADC_Value[i + 1];
                vrefint_count++;
            }

            // TEMPSENSOR (内部温度) - 索引 2, 5, 8, 11...
            if (i + 2 < 60) {
                temp_adc_sum += ADC_Value[i + 2];
                temp_count++;
            }
        }

        float adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
        float vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;
        float temp_raw = (temp_count > 0) ? (temp_adc_sum / (float)temp_count) : 0;

        // ADC平均值计算完成

        HAL_ADC_Stop_DMA(&hadc);

        // 计算和更新MCU内部温度，带VDD校正
        if (temp_raw > 0 && vrefint_raw > 0) {
            // 对温度读数应用VDD校正
            uint16_t vrefint_cal = VREFINT_CAL_VALUE;
            float actual_vdd_temp = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;
            float temp_corrected = temp_raw * actual_vdd_temp / 3.0f;

            mcu_temp = Calculate_MCU_Temperature((uint32_t)temp_corrected);
        }

        // 使用VREFINT校准计算电压
        if (vrefint_raw > 0) {
            uint16_t vrefint_cal = VREFINT_CAL_VALUE;
            float actual_vdd = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;

            // 计算基础电压（x2 用于硬件分压器）
            voltage = (adc_raw * actual_vdd / 4096.0f) * 2.0f;

        } else {
            // VREFINT失效时使用固定VDD（x2 用于硬件分压器）
            voltage = (adc_raw * 3.3f / 4096.0f) * 2.0f;
        }

        // 应用电压校准因子和偏移量（参考代码方式）
        voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;

        // 电压计算完成
    } else {
        printf("ADC DMA startup failed!\r\n");
    }

    // 关闭ADC采样开关
    VCHK_OFF;

    pw = voltage;
    printf("Battery: %.3f V\r\n", pw);

    return HAL_OK;
}

// 启用外设电源
void PowerModule_EnablePeripherals(void)
{
    // 打开GPS和传感器电源
    GPS_PWR_ON;
    V_OUT_ON;
    VCHK_ON;  // 打开ADC采样开关

    // 清除GPS缓冲区
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;

    // 确保UART接收中断已启动
    if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
        // 如果UART接收未启动，重新启动
        HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
    }
    // LPUART1使用阻塞模式，不需要中断接收

    // LED闪烁表示成功唤醒
    LED1_ON;
    osDelay(50);  // 减少延时：100ms -> 50ms
    LED1_OFF;

    // 确保I2C已初始化
    if (hi2c1.State == HAL_I2C_STATE_RESET) {
        MX_I2C1_Init();
    }
}

// 禁用外设电源
void PowerModule_DisablePeripherals(void)
{
    V_OUT_OFF;
    GPS_PWR_OFF;
    RF_PWR_OFF;   // 确保GSM电源关闭
    CAM_PW_OFF;   // 确保摄像头电源关闭
    VCHK_OFF;     // 关闭ADC采样开关
}

// GPS模块初始化
HAL_StatusTypeDef GPSModule_Init(void)
{
    // 清除GPS缓冲区
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;
    gps_new_data = 0;
    gps_data_ready = 0;

    printf("GPS module initialized\r\n");
    return HAL_OK;
}

// GPS模块上电
void GPSModule_PowerOn(void)
{
    printf("GPS: Power ON\r\n");
    GPS_PWR_ON;
    osDelay(200);
//    printf("GPS: Power ON complete, waiting for data...\r\n");
}

// GPS模块下电
void GPSModule_PowerOff(void)
{
    GPS_PWR_OFF;
}

// GPS数据清除函数
void GPSModule_ClearData(void)
{
    // 清除GPS数据结构
    memset(&gps_data, 0, sizeof(GPS_Data_t));

    // 清除GPS缓冲区
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;
    gps_new_data = 0;
    gps_data_ready = 0;

    // 注意：temp_mode_6_data 和 has_mode_6_data 是局部变量，
    // 每次GPS任务开始时会自动重新初始化，无需在此清除

    printf("GPS data cleared\r\n");
}

// 等待GPS数据
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t unused_timeout, uint8_t is_first_boot)
{
    uint32_t gps_wait_start;
    uint8_t gps_data_valid = 0;
    uint8_t has_mode_6_data = 0;
    GPS_Data_t temp_mode_6_data;  // 用于暂存模式6的数据

    // 重置GPS数据
    gps_data.valid = 0;
    gps_data_ready = 0;
    gps_new_data = 0;
    gps_buffer_index = 0;
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);

    // 设置GPS等待超时
    unsigned long  gps_wait_timeout = is_first_boot ? GPS_TIMEOUT_FIRST_BOOT * 1000 : GPS_TIMEOUT_NORMAL * 1000;

    printf("GPS wait: %lu seconds (%s boot)\r\n", gps_wait_timeout / 1000, is_first_boot ? "first" : "normal");
    printf("GPS: Starting data reception...\r\n");

    gps_wait_start = HAL_GetTick();
    uint32_t last_led_toggle = HAL_GetTick();
    uint32_t last_debug_print = HAL_GetTick();
    uint8_t led_state = 1;

    // GPS等待期间LED闪烁指示
    LED1_ON;

    // GPS等待循环
    while ((HAL_GetTick() - gps_wait_start) < gps_wait_timeout) {
        // LED每秒闪烁一次，指示GPS正在搜索
        if ((HAL_GetTick() - last_led_toggle) > 500) {
            led_state = !led_state;
            if (led_state) {
                LED1_ON;
            } else {
                LED1_OFF;
            }
            last_led_toggle = HAL_GetTick();
        }

        // 检查是否有新的GPS数据要解析
        if (gps_new_data) {
            gps_new_data = 0;
            GPS_ParseData();
        }

        // GPS状态检查调试信息
        #if GPS_DEBUG_ENABLE
        printf("GPS check: valid=%d, quality=%d, satellites=%d, lat=%.6f, lon=%.6f\r\n",
               gps_data.valid, gps_data.fix_quality, gps_data.satellites,
               gps_data.latitude_decimal, gps_data.longitude_decimal);
        #endif

        // 检查精确定位（质量1）
        if (gps_data.valid && gps_data.fix_quality == 1) {
            printf("GPS precise fix: quality=1, satellites=%d\r\n", gps_data.satellites);
            gps_data_valid = 1;
            break;  // 找到精确定位，立即退出
        }
        // 检查估算定位（质量6）- 暂存数据但继续等待质量1
        else if (gps_data.valid && gps_data.fix_quality == 6 && !has_mode_6_data) {
            printf("GPS estimated fix stored: quality=6, satellites=%d (waiting for quality 1)\r\n", gps_data.satellites);
            // 保存模式6的数据
            memcpy(&temp_mode_6_data, &gps_data, sizeof(GPS_Data_t));
            has_mode_6_data = 1;
            // 不退出循环，继续等待质量1数据
        }

        // 短暂延时，避免过度占用CPU
        osDelay(100);
    }

    // 确保在任何返回路径前LED1都关闭
    LED1_OFF;

    // GPS数据处理逻辑
    if (gps_data_valid) {
        // 收到精度1GPS数据
        return HAL_OK;
    } else if (has_mode_6_data) {
        // 没有收到精度1数据但有精度6数据，使用精度6数据
        printf("Using stored GPS quality 6 data (no quality 1 fix available)\r\n");
        memcpy(&gps_data, &temp_mode_6_data, sizeof(GPS_Data_t));
        return HAL_OK;
    } else {
        // 没有任何有效数据，处理超时
        printf("GPS timeout: no valid data received, using quality 0\r\n");

        // 使用全零默认GPS数据
        gps_data.latitude = 0.0;
        gps_data.longitude = 0.0;
        gps_data.latitude_decimal = 0.0;
        gps_data.longitude_decimal = 0.0;
        gps_data.altitude = 0.0;
        gps_data.fix_quality = 0;
        gps_data.satellites = 0;
        gps_data.speed = 0.0;
        gps_data.course = 0.0;
        gps_data.hdop = 99.9;
        gps_data.pdop = 99.9;
        gps_data.vdop = 99.9;
        gps_data.valid = 0;  // 确保标记为无效

        return HAL_TIMEOUT;
    }
}

// GPS时钟同步
HAL_StatusTypeDef GPSModule_SyncRTC(void)
{
    if (gps_data.valid && gps_data.hour <= 23 && gps_data.minute <= 59 && gps_data.second <= 59) {
        if (gps_data.year >= 2020 && gps_data.month >= 1 && gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31) {
            HAL_StatusTypeDef result = RTC_SetDateTime(
                gps_data.hour, gps_data.minute, gps_data.second,
                gps_data.day, gps_data.month, gps_data.year % 100
            );

            return result;
        } else {
            return RTC_SetTime(gps_data.hour, gps_data.minute, gps_data.second);
        }
    }
    return HAL_ERROR;
}

// 传感器模块初始化
HAL_StatusTypeDef SensorModule_Init(void)
{
    HAL_StatusTypeDef result = HAL_OK;
    //    printf("Sensor module initialization\r\n");

    // 初始化I2C
    __HAL_RCC_I2C1_CLK_ENABLE();
    MX_I2C1_Init();
    osDelay(50);  // 减少延时：100ms -> 50ms

    return result;
}

// 读取传感器数据
HAL_StatusTypeDef SensorModule_ReadData(void)
{
    // MCU温度已在PowerModule_ReadBatteryVoltage()中更新
    // 这里不需要再次更新，因为ADC_Value[]可能包含陈旧数据

    if (HAL_I2C_IsDeviceReady(&hi2c1, LSM6DS3_ADDR, 3, 100) == HAL_OK) {
        LSM6DS3_Init(&hi2c1);
        // 只在attitude未初始化时才初始化，避免每次都重置为0
        if (!attitude.initialized) {
            LSM6DS3_InitAttitude(&attitude);
        }
        osDelay(20);  // 减少延时：50ms -> 20ms
        LSM6DS3_ReadData(&hi2c1, &imuData);
        LSM6DS3_ComplementaryFilter(&imuData, &attitude);

        // 显示一次Roll和Pitch值
        printf("Roll=%.1f, Pitch=%.1f\r\n", attitude.roll, attitude.pitch);

				printf("GPS signal waiting......\r\n");


        return HAL_OK;
    } else {
        memset(&imuData, 0, sizeof(LSM6DS3_Data));
        imuData.temp_celsius = 25.0f;
        memset(&attitude, 0, sizeof(LSM6DS3_Attitude));
        return HAL_ERROR;
    }
}

// 创建数据包
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size)
{
    return Create_Data_String(output_buffer, buffer_size);
}

// 打印数据
void DataModule_PrintData(const char *data_string)
{
    printf("TX Data output: %s\r\n", data_string);
}

// 打印当前时间
void SystemModule_PrintCurrentTime(void)
{
    RTC_TimeTypeDef time;
    RTC_DateTypeDef date;
    RTC_GetDateTime(&time, &date);

    // 保存到全局变量供工作时间检测使用
    current_wakeup_time = time;
    current_wakeup_date = date;

    // 检查RTC时间是否为默认值（01/01/25），如果是则认为未同步
    if (date.Date == 1 && date.Month == 1 && date.Year == 25) {
        rtc_time_valid = 0;  // RTC时间无效（默认时间）
        printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d (DEFAULT - NOT SYNCED)\r\n",
               time.Hours, time.Minutes, time.Seconds,
               date.Date, date.Month, date.Year);
    } else {
        rtc_time_valid = 1;  // RTC时间有效（已同步）
        printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
               time.Hours, time.Minutes, time.Seconds,
               date.Date, date.Month, date.Year);
    }
}

// 系统进入休眠模式
void SystemModule_EnterSleepMode(void)
{
    // 确保所有LED在休眠前都关闭（修复LED常亮BUG）
    LED1_OFF;

    // 关闭GSM连接和电源
    if (GSM_GetState() == GSM_STATE_CONNECTED) {
        GSM_CloseServer();
        osDelay(500);  // 发送关闭指令后等待500ms
    }

    // 关闭GSM电源
    GSM_PowerOff();

    // 禁用外设电源
    PowerModule_DisablePeripherals();

    // 打印休眠前时间
    RTC_TimeTypeDef time_before_sleep;
    RTC_DateTypeDef date_before_sleep;
    HAL_RTC_GetTime(&hrtc, &time_before_sleep, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date_before_sleep, RTC_FORMAT_BIN);
    printf("Time before sleep: %02d:%02d:%02d\r\n",
           time_before_sleep.Hours, time_before_sleep.Minutes, time_before_sleep.Seconds);

    // 休眠前二次检查工作时间段（GPS可能已同步RTC）
    NetworkCommand_CheckWorkTime();

    // 检查是否已经有设置的wakeup_counter（来自网络指令或智能休眠）
    uint32_t sleep_seconds;

    if (wakeup_counter > 0) {
        // 使用已设置的wakeup_counter（来自网络指令）
        sleep_seconds = wakeup_counter + 1;
        printf("Using pre-set sleep time: %lu seconds\r\n", sleep_seconds);
    } else {
        // 从FLASH读取休眠时间设置，如果没有设置则使用默认值
        sleep_seconds = SLEEP_DURATION_SECONDS;  // 默认值

        NetworkCommand_Result_t sleep_result;
        // 尝试读取任何类型的休眠时间设置
        if (NetworkCommand_LoadFromFlash(CMD_TYPE_SLEEP_DAYS, &sleep_result) == HAL_OK && sleep_result.is_valid) {
            // 根据指令类型转换为秒数
            switch (sleep_result.type) {
                case CMD_TYPE_SLEEP_DAYS:
                    sleep_seconds = sleep_result.value1 * 24 * 3600;
                    printf("Using sleep setting from FLASH: %lu days (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_HOURS:
                    sleep_seconds = sleep_result.value1 * 3600;
                    printf("Using sleep setting from FLASH: %lu hours (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_MINUTES:
                    sleep_seconds = sleep_result.value1 * 60;
                    printf("Using sleep setting from FLASH: %lu minutes (%lu seconds)\r\n", sleep_result.value1, sleep_seconds);
                    break;
                case CMD_TYPE_SLEEP_SECONDS:
                    sleep_seconds = sleep_result.value1;
                    printf("Using sleep setting from FLASH: %lu seconds\r\n", sleep_seconds);
                    break;
                default:
                    printf("Invalid sleep command type in FLASH, using default: %d seconds\r\n", SLEEP_DURATION_SECONDS);
                    break;
            }

            // 更新wakeup_counter
            wakeup_counter = sleep_seconds - 1;
        } else {
            printf("No sleep setting in FLASH, using default: %d seconds\r\n", SLEEP_DURATION_SECONDS);
            wakeup_counter = SLEEP_DURATION_SECONDS - 1;
        }
    }

    printf("Sleep for %lu seconds\r\n", sleep_seconds);

    // 进入低功耗模式（会在EnterStopMode中应用RTC修正）
    EnterStopMode();

    // 获取实际执行的逻辑时间（从EnterStopMode中的RTC修正结果）
    extern uint32_t last_actual_logical_seconds;

    // 更新长时间休眠进度（使用实际执行的逻辑时间）
    NetworkCommand_UpdateLongSleepProgress(last_actual_logical_seconds);

    printf("=======================END==========================\r\n");
		printf("\r\n");
    printf("\r\n");
}

// 电池保护检查功能
uint8_t BatteryProtection_CheckVoltage(void)
{
    // 使用已经读取的电池电压值，避免重复读取
    float current_voltage = pw;

    if (current_voltage < BATTERY_LOW_VOLTAGE_THRESHOLD) {
        if (!low_voltage_protection_active) {
            printf("Battery protection activated: %.3fV < %.1fV\r\n",
                   current_voltage, BATTERY_LOW_VOLTAGE_THRESHOLD);
            low_voltage_protection_active = 1;
        }
        return 1;  // 需要进入低电压保护
    } else if (current_voltage >= BATTERY_RECOVERY_VOLTAGE_THRESHOLD && low_voltage_protection_active) {
        low_voltage_protection_active = 0;
        return 2;  // 电压恢复，退出保护模式
    }

    return 0;  // 电压正常
}

// 低电压保护模式
void BatteryProtection_EnterLowVoltageMode(void)
{
    printf("*** Low voltage protection activated ***\r\n");
    printf("Battery voltage: %.3fV < %.1fV (threshold)\r\n",
           pw, BATTERY_LOW_VOLTAGE_THRESHOLD);

    // 确保所有LED关闭
    LED1_OFF;

    // 关闭所有外设
    PowerModule_DisablePeripherals();
    // 删除ADC_OFF调用，硬件没有这个功能
    V_OUT_OFF;  // 关闭外设电源
    GPS_PWR_OFF;  // 关闭GPS电源

    printf("Device entering low power protection mode\r\n");

    // 延长睡眠时间到低电压保护周期
    vTaskDelay(pdMS_TO_TICKS(SLEEP_DURATION_SECONDS * 1000));
}

// 退出低电压保护模式
void BatteryProtection_ExitLowVoltageMode(void)
{
    printf("*** Voltage recovery detected ***\r\n");
    printf("Battery voltage: %.3fV > %.1fV (recovery threshold)\r\n",
           pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);

    // 重新使能外设
    PowerModule_EnablePeripherals();

    printf("Device resumed normal operation\r\n");
}

// STM32内部温度传感器计算函数实现
/**
  * @brief  计算STM32内部温度传感器的温度值
  * @param  temp_adc_value: 温度传感器的ADC原始值
  * @retval 计算得到的温度值（摄氏度）
  */
float Calculate_MCU_Temperature(uint32_t temp_adc_value)
{
    // STM32L071工厂校准值地址 - 修正为130°C校准点
    uint16_t *temp30_cal = (uint16_t*)0x1FF8007A;  // 30°C时的校准值
    uint16_t *temp130_cal = (uint16_t*)0x1FF8007E; // 130°C时的校准值（修正）

    // 读取工厂校准值
    uint16_t cal_30 = *temp30_cal;
    uint16_t cal_130 = *temp130_cal;

    // 使用线性插值计算温度 - 修正为30°C到130°C范围
    // 公式: Temperature = 30 + (ADC_Value - CAL_30) * (130 - 30) / (CAL_130 - CAL_30)
    float temperature = 30.0f + ((float)temp_adc_value - (float)cal_30) * 100.0f / ((float)cal_130 - (float)cal_30);

    // 应用温度校准系数和偏移量
    #include "GPS.h"  // 包含温度校准宏定义
    temperature = temperature * TEMP_CALIBRATION_FACTOR + TEMP_OFFSET;

    return temperature;
}

// 时间管理函数实现

// 获取当前累计唤醒时间（分钟）
uint32_t GetCurrentWakeupTime(void)
{
    // 获取当前使用的休眠时间
    uint32_t sleep_seconds = SLEEP_DURATION_SECONDS;  // 默认值

    NetworkCommand_Result_t sleep_result;
    if (NetworkCommand_LoadFromFlash(CMD_TYPE_SLEEP_DAYS, &sleep_result) == HAL_OK && sleep_result.is_valid) {
        switch (sleep_result.type) {
            case CMD_TYPE_SLEEP_DAYS:
                sleep_seconds = sleep_result.value1 * 24 * 3600;
                break;
            case CMD_TYPE_SLEEP_HOURS:
                sleep_seconds = sleep_result.value1 * 3600;
                break;
            case CMD_TYPE_SLEEP_MINUTES:
                sleep_seconds = sleep_result.value1 * 60;
                break;
            case CMD_TYPE_SLEEP_SECONDS:
                sleep_seconds = sleep_result.value1;
                break;
            default:
                break;
        }
    }

    return device_wakeup_count * sleep_seconds / 60;
}

/* USER CODE END Application */

