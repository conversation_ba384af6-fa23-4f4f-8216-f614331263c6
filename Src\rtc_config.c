/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    rtc_config.c
  * @brief   RTC时钟修正功能实现
  ******************************************************************************
  * @attention
  *
  * RTC时钟修正功能，用于补偿LSI时钟源的精度问题
  *
  ******************************************************************************
  */
/* USER CODE END Header */

#include "rtc_config.h"
#include "main.h"
#include <stdio.h>

/**
 * @brief  应用RTC时钟修正
 * @param  logical_seconds: 请求的逻辑休眠时间（秒）
 * @param  actual_logical_seconds: 输出实际能执行的逻辑时间（秒）
 * @retval 返回RTC应该设置的物理时间（秒）
 */
uint32_t RTC_ApplyClockCorrection(uint32_t logical_seconds, uint32_t *actual_logical_seconds)
{
    uint32_t max_logical_time = RTC_MAX_LOGICAL_SLEEP_SECONDS;
    uint32_t actual_logical_time;
    uint32_t physical_rtc_time;

    // 运行时验证最大逻辑时间设置是否正确
    static uint8_t verified = 0;
    if (!verified) {
        uint32_t test_physical = (uint32_t)(RTC_MAX_LOGICAL_SLEEP_SECONDS * RTC_CLOCK_CORRECTION_FACTOR);
        if (test_physical > RTC_MAX_SLEEP_SECONDS_RAW) {
            printf("WARNING: RTC_MAX_LOGICAL_SLEEP_SECONDS too large! %lu * %.2f = %lu > %u\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, RTC_CLOCK_CORRECTION_FACTOR, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        } else {
            printf("RTC correction config verified: %lu * %.2f = %lu <= %u\r\n",
                   RTC_MAX_LOGICAL_SLEEP_SECONDS, RTC_CLOCK_CORRECTION_FACTOR, test_physical, RTC_MAX_SLEEP_SECONDS_RAW);
        }
        verified = 1;
    }

    // 确定实际能执行的逻辑时间
    if (logical_seconds <= max_logical_time) {
        actual_logical_time = logical_seconds;
    } else {
        actual_logical_time = max_logical_time;
        printf("RTC correction limited logical time from %lu to %lu seconds\r\n",
               logical_seconds, actual_logical_time);
    }

    // 计算RTC应该设置的物理时间
    physical_rtc_time = (uint32_t)(actual_logical_time * RTC_CLOCK_CORRECTION_FACTOR);

    // 安全检查，防止溢出
    if (physical_rtc_time > RTC_MAX_SLEEP_SECONDS_RAW) {
        physical_rtc_time = RTC_MAX_SLEEP_SECONDS_RAW;
        printf("RTC physical time limited to maximum: %lu seconds\r\n", physical_rtc_time);
    }

    // 输出实际能执行的逻辑时间
    if (actual_logical_seconds != NULL) {
        *actual_logical_seconds = actual_logical_time;
    }

    // 调试输出
    if (logical_seconds != actual_logical_time) {
        printf("RTC correction: requested=%lu, actual_logical=%lu, physical=%lu seconds\r\n",
               logical_seconds, actual_logical_time, physical_rtc_time);
    } else {
        printf("RTC correction: logical=%lu → physical=%lu seconds (factor=%.2f)\r\n",
               actual_logical_time, physical_rtc_time, RTC_CLOCK_CORRECTION_FACTOR);
    }

    return physical_rtc_time;
}
